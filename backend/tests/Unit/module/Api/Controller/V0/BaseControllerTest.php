<?php

namespace tests\Unit\module\Api\Controller\V0;

use Api\Controller\V0\BaseController;
use Exception;
use Laminas\Http\Response;
use Laminas\Mvc\Application;
use Laminas\Mvc\Controller\Plugin\CreateHttpNotFoundModel;
use <PERSON><PERSON>\Mvc\Controller\PluginManager;
use Laminas\Mvc\MvcEvent;
use Laminas\Router\RouteMatch;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use RuntimeException;
use STApi\Controller\Plugin\ApiPermissionChecker;
use STCompany\Entity\Company;
use STConfiguration\Service\ConfigurationService;
use STLib\Expand\Collection;
use STLib\IdGenerator\IdGeneratorService;
use STRoboTruck\Controller\Plugin\RoboTruck;
use STRoboTruck\Service\ExceptionCollectorService;
use STRoboTruck\Service\RequestCollectorService;
use STUser\Entity\User;
use STUser\Service\AuthService;
use tests\TestCase;
use Throwable;

class BaseControllerTest extends TestCase
{
    /**
     * @throws Throwable
     */
    public function testOnDispatch(): void
    {
        $action = 'action name';
        $routeMatchMap = [
            ['access-checks', null, [BaseController::CHECK_API_ACCESS]],
            ['action', null, $action],
            ['action', 'not-found', 'not-found'],
        ];
        $routeMatch = $this->createMock(RouteMatch::class);
        $routeMatch
            ->method('getParam')
            ->willReturnMap($routeMatchMap);

        $response = $this->createMock(Response::class);
        $mvcEvent = $this->createMock(MvcEvent::class);
        $mvcEvent
            ->method('getRouteMatch')
            ->willReturn($routeMatch);
        $mvcEvent
            ->method('getResponse')
            ->willReturn($response);

        $application = $this->createMock(Application::class);
        $application
            ->method('getMvcEvent')
            ->willReturn($mvcEvent);

        $user = $this->createMock(User::class);

        $authService = $this->createMock(AuthService::class);
        $authService
            ->method('isSigned')
            ->willReturn(true);
        $authService
            ->method('getUser')
            ->willReturn($user);

        $company = $this->createMock(Company::class);

        $this->serviceManager->setService('application', $application);
        $apiPermissionChecker = $this->createMock(ApiPermissionChecker::class);
        $apiPermissionChecker
            ->method('checkApiAccess')
            ->willReturn($company);

        $createHttpNotFoundModel = $this->createMock(CreateHttpNotFoundModel::class);
        $createHttpNotFoundModel
            ->method('__invoke')
            ->willReturn(function () {
            });

        $requestCollectorService = $this->createMock(RequestCollectorService::class);

        $roboTruckPlugin = $this->createMock(RoboTruck::class);
        $roboTruckPlugin
            ->method('requestCollector')
            ->willReturn($requestCollectorService);

        $pluginManagerMap = [
            ['apiPermissionChecker', null, $apiPermissionChecker],
            ['auth', null, $authService],
            ['createHttpNotFoundModel', null, $createHttpNotFoundModel],
            ['roboTruck', null, $roboTruckPlugin],
        ];
        $pluginManager = $this->createMock(PluginManager::class);
        $pluginManager
            ->method('get')
            ->willReturnMap($pluginManagerMap);

        $controller = $this->createPartialMock(
            BaseController::class,
            ['initActiveFront', 'restoreUserToken', 'getApiParams']
        );
        $controller->setServiceManager($this->serviceManager);
        $controller->setPluginManager($pluginManager);
        $controller->setEvent($mvcEvent);

        $parameters = [
            'key 1' => 'value 1',
            'key 2' => 'value 2',
        ];
        $parametersCollection = new Collection($parameters);
        $controller
            ->method('getApiParams')
            ->willReturn($parametersCollection);

        $requestCollectorService
            ->expects($this->once())
            ->method('collect')
            ->with($controller::class, $mvcEvent, $parametersCollection, $user, $company);

        $controller->onDispatch($mvcEvent);
    }

    /**
     * @throws Throwable
     * @throws PHPUnitException
     */
    public function testOnDispatchWhenSomeError(): void
    {
        $routeMatch = $this->createMock(RouteMatch::class);
        $routeMatch
            ->method('getParam')
            ->willReturn([BaseController::CHECK_API_ACCESS]);

        $mvcEvent = $this->createMock(MvcEvent::class);
        $mvcEvent
            ->method('getRouteMatch')
            ->willReturn($routeMatch);

        $application = $this->createMock(Application::class);
        $application
            ->method('getMvcEvent')
            ->willReturn($mvcEvent);

        $user = $this->createMock(User::class);

        $authService = $this->createMock(AuthService::class);
        $authService
            ->method('isSigned')
            ->willReturn(true);
        $authService
            ->method('getUser')
            ->willReturn($user);

        $exception = new RuntimeException();
        $this->serviceManager->setService('application', $application);
        $apiPermissionChecker = $this->createMock(ApiPermissionChecker::class);
        $apiPermissionChecker
            ->method('checkApiAccess')
            ->willThrowException($exception);

        $requestCollector = $this->createMock(RequestCollectorService::class);
        $exceptionIdGenerator = $this->createMock(IdGeneratorService::class);
        $exceptionCollector = $this->createMock(ExceptionCollectorService::class);

        $roboTruckPlugin = $this->createMock(RoboTruck::class);
        $roboTruckPlugin
            ->method('requestCollector')
            ->willReturn($requestCollector);
        $roboTruckPlugin
            ->method('exceptionCollector')
            ->willReturn($exceptionCollector);
        $roboTruckPlugin
            ->method('exceptionIdGenerator')
            ->willReturn($exceptionIdGenerator);

        $configuration = $this->createMock(ConfigurationService::class);
        $configuration
            ->method('get')
            ->with('api')
            ->willReturn(['debug' => false]);

        $pluginManagerMap = [
            ['apiPermissionChecker', null, $apiPermissionChecker],
            ['auth', null, $authService],
            ['roboTruck', null, $roboTruckPlugin],
            ['configuration', null, $configuration],
        ];
        $pluginManager = $this->createMock(PluginManager::class);
        $pluginManager
            ->method('get')
            ->willReturnMap($pluginManagerMap);

        $controller = $this->createPartialMock(
            BaseController::class,
            ['initActiveFront', 'restoreUserToken', 'getApiParams']
        );
        $controller->setServiceManager($this->serviceManager);
        $controller->setPluginManager($pluginManager);
        $controller->setEvent($mvcEvent);

        $parameters = [
            'key 1' => 'value 1',
            'key 2' => 'value 2',
        ];
        $parametersCollection = new Collection($parameters);
        $controller
            ->method('getApiParams')
            ->willReturn($parametersCollection);

        $requestCollector
            ->expects($this->once())
            ->method('collect')
            ->with($controller::class, $mvcEvent, $parametersCollection, $user, null);

        $someExceptionId = $this->faker->text();
        $exceptionIdGenerator
            ->method('generatePseudoUniqueId')
            ->willReturn($someExceptionId);

        $exceptionCollector
            ->expects($this->once())
            ->method('collect')
            ->with($exception, $someExceptionId);

        $response = $controller->onDispatch($mvcEvent);

        $this->assertNotEmpty($response->getContent());
        $content = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('error', $content);
        $this->assertArrayHasKey('id', $content['error']);
    }

    /**
     * @throws Throwable
     * @throws PHPUnitException
     */
    public function testOnDispatchWhenNoAuthUser(): void
    {
        $routeMatch = $this->createMock(RouteMatch::class);
        $routeMatch
            ->method('getParam')
            ->willReturn([BaseController::CHECK_API_ACCESS]);

        $mvcEvent = $this->createMock(MvcEvent::class);
        $mvcEvent
            ->method('getRouteMatch')
            ->willReturn($routeMatch);

        $application = $this->createMock(Application::class);
        $application
            ->method('getMvcEvent')
            ->willReturn($mvcEvent);

        $authService = $this->createMock(AuthService::class);
        $authService
            ->method('isSigned')
            ->willReturn(false);
        $authService
            ->method('getUser')
            ->willThrowException(new Exception());

        $this->serviceManager->setService('application', $application);
        $apiPermissionChecker = $this->createMock(ApiPermissionChecker::class);
        $apiPermissionChecker
            ->method('checkApiAccess')
            ->willThrowException(new RuntimeException(code: 123));

        $requestCollectorService = $this->createMock(RequestCollectorService::class);

        $roboTruckPlugin = $this->createMock(RoboTruck::class);
        $roboTruckPlugin
            ->method('requestCollector')
            ->willReturn($requestCollectorService);

        $configuration = $this->createMock(ConfigurationService::class);
        $configuration
            ->method('get')
            ->with('api')
            ->willReturn(['debug' => true]);

        $pluginManagerMap = [
            ['apiPermissionChecker', null, $apiPermissionChecker],
            ['auth', null, $authService],
            ['roboTruck', null, $roboTruckPlugin],
            ['configuration', null, $configuration],
        ];
        $pluginManager = $this->createMock(PluginManager::class);
        $pluginManager
            ->method('get')
            ->willReturnMap($pluginManagerMap);

        $controller = $this->createPartialMock(
            BaseController::class,
            ['initActiveFront', 'restoreUserToken', 'getApiParams']
        );
        $controller->setServiceManager($this->serviceManager);
        $controller->setPluginManager($pluginManager);
        $controller->setEvent($mvcEvent);

        $parameters = [
            'key 1' => 'value 1',
            'key 2' => 'value 2',
        ];
        $parametersCollection = new Collection($parameters);
        $controller
            ->method('getApiParams')
            ->willReturn($parametersCollection);

        $requestCollectorService
            ->expects($this->once())
            ->method('collect')
            ->with($controller::class, $mvcEvent, $parametersCollection, null, null);

        $this->expectException(RuntimeException::class);
        $controller->onDispatch($mvcEvent);
    }

    public function testSanitizeApiParams(): void
    {
        $controller = new class extends BaseController {
            public function exposeSanitizeApiParams(array $params): array
            {
                return $this->sanitizeApiParams($params);
            }
        };

        // Test string sanitization
        $input = [
            'simple_string' => '  hello world  ',
            'with_linebreaks' => "line1\n\n\nline2\r\n\r\nline3\r\rline4",
            'mixed_whitespace' => "  \t  spaced text  \t  ",
            'number' => 123,
            'boolean' => true,
            'null_value' => null,
            'nested_array' => [
                'nested_string' => '  nested value  ',
                'nested_breaks' => "nested\n\n\ntext",
                'deeply_nested' => [
                    'deep_string' => '  deep value  ',
                    'deep_breaks' => "deep\r\n\r\n\r\ntext"
                ]
            ]
        ];

        $expected = [
            'simple_string' => 'hello world',
            'with_linebreaks' => "line1\nline2\nline3\nline4",
            'mixed_whitespace' => 'spaced text',
            'number' => 123,
            'boolean' => true,
            'null_value' => null,
            'nested_array' => [
                'nested_string' => 'nested value',
                'nested_breaks' => "nested\ntext",
                'deeply_nested' => [
                    'deep_string' => 'deep value',
                    'deep_breaks' => "deep\ntext"
                ]
            ]
        ];

        $result = $controller->exposeSanitizeApiParams($input);
        $this->assertEquals($expected, $result);
    }

    public function testSanitizeApiParamsWithEmptyValues(): void
    {
        $controller = new class extends BaseController {
            public function exposeSanitizeApiParams(array $params): array
            {
                return $this->sanitizeApiParams($params);
            }
        };

        $input = [
            'empty_string' => '',
            'whitespace_only' => '   ',
            'linebreaks_only' => "\n\n\r\n",
            'empty_array' => []
        ];

        $expected = [
            'empty_string' => '',
            'whitespace_only' => '',
            'linebreaks_only' => "\n",
            'empty_array' => []
        ];

        $result = $controller->exposeSanitizeApiParams($input);
        $this->assertEquals($expected, $result);
    }
}
