<?php

declare(strict_types=1);

namespace Api\Controller\V0;

use Carbon\Carbon;
use Exception;
use <PERSON>inas\Http\Header\ContentType;
use <PERSON>inas\Http\Headers;
use <PERSON><PERSON>\Stdlib\ResponseInterface;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use STApi\Entity\Exception\BadRequestApiException;
use STApi\Entity\Exception\NotFoundApiException;
use STApi\Entity\Exception\ThirdPartyApiException;
use STApi\Entity\Exception\ValidationApiException;
use STCompany\Entity\Checklist\ChecklistPoint;
use STCompany\Entity\Checklist\Checklist;
use STCompany\Validator\Checklist\ChangeOrderValidator;
use STCompany\Validator\Checklist\ImproveChecklistPointValidator;
use STCompany\Validator\Checklist\SaveChecklistPointValidator;
use STCompany\Validator\Checklist\SaveChecklistValidator;
use STLib\Mvc\Hydrator\BaseHydratorTrait;

class ChecklistController extends BaseController
{
    use BaseHydratorTrait;

    /**
     * @return array
     * @throws BadRequestApiException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function agentsTotalStatisticsAction(): array
    {
        $params = $this->getStatisticsParams();

        return $this->formatStatisticsResponse(
            [
                'agents' => $this->call()->checklist()->getAgentsTotalStatistics(
                    $params['companyId'],
                    $this->auth()->getUser()->getId(),
                    $params['checklistId'],
                    null,
                    $params['startDate'],
                    $params['endDate'],
                )
            ],
            $this->getChecklistPointLastUpdate($params['companyId'], $params['checklistId'])
        );
    }

    /**
     * @return array
     * @throws BadRequestApiException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function agentCallsStatisticsAction(): array
    {
        $params = $this->getStatisticsParams();
        $agentId = (int) $this->validateEntityIdParam('agent_id', 'agent_id param is not provided');

        return $this->formatStatisticsResponse(
            $this->call()->checklist()->getAgentCallsStatistics(
                $agentId,
                $params['companyId'],
                $this->auth()->getUser()->getId(),
                $params['checklistId'],
                $params['startDate'],
                $params['endDate'],
            ),
            $this->getChecklistPointLastUpdate($params['companyId'], $params['checklistId'])
        );
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws BadRequestApiException
     */
    public function clientsTotalStatisticsAction(): array
    {
        $params = $this->getStatisticsParams();

        return $this->formatStatisticsResponse(
            [
                'clients' => $this->call()->checklist()->getClientsTotalStatistics(
                    $params['companyId'],
                    $this->auth()->getUser()->getId(),
                    $params['checklistId'],
                    null,
                    $params['startDate'],
                    $params['endDate'],
                )
            ],
            $this->getChecklistPointLastUpdate($params['companyId'], $params['checklistId']),
        );
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws BadRequestApiException
     */
    public function clientCallsStatisticsAction(): array
    {
        $params = $this->getStatisticsParams();
        $clientId = $this->validateEntityIdParam('client_id', 'client_id param is not provided');

        return $this->formatStatisticsResponse(
            $this->call()->checklist()->getClientCallsStatistics(
                $clientId,
                $params['companyId'],
                $this->auth()->getUser()->getId(),
                $params['checklistId'],
                $params['startDate'],
                $params['endDate'],
            ),
            $this->getChecklistPointLastUpdate($params['companyId'], $params['checklistId'])
        );
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws BadRequestApiException
     */
    public function campaignsTotalStatisticsAction(): array
    {
        $params = $this->getStatisticsParams();

        return $this->formatStatisticsResponse(
            [
                'campaigns' => $this->call()->checklist()->getCampaignsTotalStatistics(
                    $params['companyId'],
                    $this->auth()->getUser()->getId(),
                    $params['checklistId'],
                    null,
                    $params['startDate'],
                    $params['endDate'],
                )
            ],
            $this->getChecklistPointLastUpdate($params['companyId'], $params['checklistId']),
        );
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws BadRequestApiException
     */
    public function campaignCallsStatisticsAction(): array
    {
        $params = $this->getStatisticsParams();
        $campaignId = $this->validateEntityIdParam('campaign_id', 'campaign_id param is not provided');

        return $this->formatStatisticsResponse(
            $this->call()->checklist()->getCampaignCallsStatistics(
                $campaignId,
                $this->auth()->getUser()->getId(),
                $params['companyId'],
                $params['checklistId'],
                $params['startDate'],
                $params['endDate'],
            ),
            $this->getChecklistPointLastUpdate($params['companyId'], $params['checklistId']),
        );
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws BadRequestApiException
     */
    public function sourcesTotalStatisticsAction(): array
    {
        $params = $this->getStatisticsParams();

        return $this->formatStatisticsResponse(
            [
                'sources' => $this->call()->checklist()->getSourcesTotalStatistics(
                    $params['companyId'],
                    $this->auth()->getUser()->getId(),
                    $params['checklistId'],
                    null,
                    $params['startDate'],
                    $params['endDate'],
                )
            ],
            $this->getChecklistPointLastUpdate($params['companyId'], $params['checklistId']),
        );
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws BadRequestApiException
     */
    public function sourceCallsStatisticsAction(): array
    {
        $params = $this->getStatisticsParams();
        $source = $this->validateEntityIdParam('source', 'source param is not provided');

        return $this->formatStatisticsResponse(
            $this->call()->checklist()->getSourceCallsStatistics(
                $source,
                $params['companyId'],
                $this->auth()->getUser()->getId(),
                $params['checklistId'],
                $params['startDate'],
                $params['endDate'],
            ),
            $this->getChecklistPointLastUpdate($params['companyId'], $params['checklistId']),
        );
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws BadRequestApiException
     */
    public function teamsTotalStatisticsAction(): array
    {
        $params = $this->getStatisticsParams();

        return $this->formatStatisticsResponse(
            [
                'teams' => $this->call()->checklist()->getTeamsTotalStatistics(
                    $params['companyId'],
                    $this->auth()->getUser()->getId(),
                    $params['checklistId'],
                    null,
                    $params['startDate'],
                    $params['endDate'],
                )
            ],
            $this->getChecklistPointLastUpdate($params['companyId'], $params['checklistId']),
        );
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws BadRequestApiException
     */
    public function teamCallsStatisticsAction(): array
    {
        $params = $this->getStatisticsParams();
        $teamId = (int) $this->validateEntityIdParam('team_id', 'team_id param is not provided');

        return $this->formatStatisticsResponse(
            $this->call()->checklist()->getTeamCallsStatistics(
                $teamId,
                $params['companyId'],
                $this->auth()->getUser()->getId(),
                $params['checklistId'],
                $params['startDate'],
                $params['endDate'],
            ),
            $this->getChecklistPointLastUpdate($params['companyId'], $params['checklistId'])
        );
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws BadRequestApiException
     */
    public function callsStatisticsAction(): array
    {
        $params = $this->getStatisticsParams();

        return $this->formatStatisticsResponse(
            $this->call()->checklist()->getCallsStatistics(
                $params['companyId'],
                $this->auth()->getUser()->getId(),
                $params['checklistId'],
                $params['startDate'],
                $params['endDate'],
            ),
            $this->getChecklistPointLastUpdate($params['companyId'], $params['checklistId'])
        );
    }

    /**
     * @throws NotFoundApiException
     */
    public function getChecklistAction(): array
    {
        return [
            'checklist' => $this->company()->checklist()->getChecklist(
                (int) $this->getApiParam('checklist_id'),
                $this->company->getId(),
            )?->toArray(),
        ];
    }

    /**
     */
    public function getChecklistsAction(): array
    {
        return [
            'checklists' => $this->company()->checklist()->getCompanyChecklistsSummary($this->company->getId()),
        ];
    }

    /**
     * @throws NotFoundApiException
     */
    public function getChecklistPointAction(): array
    {
        return [
            'checklist_point' => $this->company()->checklistPoint()->getChecklistPoint(
                (int) $this->getApiParam('checklist_point_id'),
                $this->company->getId(),
            )->toArray(),
        ];
    }

    /**
     * @throws NotFoundApiException
     */
    public function getChecklistPointsAction(): array
    {
        $checklistId = (int) $this->getApiParam('checklist_id');
        $companyId = $this->company->getId();

        $this->company()->checklist()->getChecklist($checklistId, $companyId);

        return [
            'checklists_points' => $this->company()->checklistPoint()->getChecklistPointsByChecklistId(
                $checklistId,
                $companyId
            )->toArray(),
        ];
    }

    /**
     * @return array
     * @throws ContainerExceptionInterface
     * @throws NotFoundApiException
     * @throws NotFoundExceptionInterface
     * @throws ValidationApiException
     * @throws Exception
     */
    public function changeOrderAction(): array
    {
        $checklistId = (int) $this->getApiParam('checklist_id');

        $this->company()->checklist()->getChecklist($checklistId, $this->company->getId());

        $orderedIds = $this->getApiParam('order');

        /** @var ChangeOrderValidator $validator */
        $validator = $this->getServiceManager()->get(ChangeOrderValidator::class);
        $validator->setInstance(
            ['checklist_id' => $checklistId, 'company_id' => $this->company->getId(), 'ordered_ids' => $orderedIds]
        );
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $this->company()->checklistPoint()->changeOrder($orderedIds);

        return ['order' => $orderedIds];
    }

    public function improveChecklistPointAction(): array|ResponseInterface
    {
        $title = $this->getApiParam('title');
        $expectedActions = $this->getApiParam('expected_actions');
        $goodPerformanceDescription = $this->getApiParam('good_performance_description');
        $badPerformanceDescription = $this->getApiParam('bad_performance_description');

        /** @var ImproveChecklistPointValidator $validator */
        $validator = $this->getServiceManager()->get(ImproveChecklistPointValidator::class);
        $validator->setInstance($this->getApiParams()->toArray());
        $this->validate($validator);

        try {
            $result = $this
            ->algo()
            ->aiSolutionsCommutator()
            ->improveChecklistPoint($title, $expectedActions, $goodPerformanceDescription, $badPerformanceDescription);
        } catch (ThirdPartyApiException $e) {
            $header = new ContentType('application/json');
            $this->getResponse()->getHeaders()->addHeader($header);
            $this->getResponse()->setStatusCode(502);
            $this->getResponse()->setContent(json_encode(['error' => ['messages' => $e->getMessage()]]));

            return $this->getResponse();
        }

        return [
            'checklist_point' => $result,
        ];
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ValidationApiException
     * @throws \Exception
     */
    public function saveChecklistAction(): array
    {
        /** @var SaveChecklistValidator $validator */
        $validator = $this->getServiceManager()->get(SaveChecklistValidator::class);
        $validator->setInstance(
            array_merge(
                $this->getApiParams()->toArray(),
                [
                    'company_id' => $this->company->getId(),
                    'user' => $this->auth()->getUser(),
                ]
            ),
        );
        $this->validate($validator);

        /* @var Checklist $checklist */
        $checklist = $this->hydrate(
            array_filter(
                [
                    'id' => $this->getApiParam('checklist_id') ? (int) $this->getApiParam('checklist_id') : null,
                    'companyId' => $this->company->getId(),
                    'name' => $this->getApiParam('checklist_name'),
                    'callDurationThreshold' => $this->getApiParam('call_duration_threshold'),
                    'callsTeams' => $this->getApiParam('calls_teams'),
                    'callsScope' => $this->getApiParam('calls_scope'),
                    'callsStatuses' => $this->getApiParam('calls_statuses'),
                    'description' => $this->getApiParam('description'),
                ],
                static fn($value) => $value !== null,
            ),
            Checklist::class,
            true,
        );

        $checklist = $this->company()->checklist()->save($checklist);

        return [
            'checklist' => $checklist->toArray(),
        ];
    }

    /**
     * @return array
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ValidationApiException
     * @throws ReflectionException
     */
    public function saveChecklistPointAction(): array
    {
        /** @var SaveChecklistPointValidator $validator */
        $validator = $this->getServiceManager()->get(SaveChecklistPointValidator::class);
        $validator->setInstance(
            array_merge(
                $this->getApiParams()->toArray(),
                ['company_id' => $this->company->getId()]
            ),
        );
        $this->validate($validator);

        /* @var ChecklistPoint $checklistPoint */
        $checklistPoint = $this->hydrate(
            [
                'checklist_id' => $this->getApiParam('checklist_id'),
                'checklist_point_id' => $this->getApiParam('checklist_point_id') ? (int) $this->getApiParam(
                    'checklist_point_id'
                ) : null,
                'title' => $this->getApiParam('title') ? trim($this->getApiParam('title')) : null,
                'description' => $this->getApiParam('description'),
                'expected_actions' => $this->getApiParam('expected_actions'),
                'good_performance_description' => $this->getApiParam('good_performance_description'),
                'bad_performance_description' => $this->getApiParam('bad_performance_description'),
                'good_performance_example' => $this->getApiParam('good_performance_example'),
                'bad_performance_example' => $this->getApiParam('bad_performance_example'),
                'is_optional' => (bool) ($this->getApiParam('is_optional') ?? false),
                'trigger_condition' => $this->getApiParam('trigger_condition'),
            ],
            ChecklistPoint::class
        );

        $checklistPoint = $this->company()->checklistPoint()->save(
            $checklistPoint,
        );

        return [
            'checklistPoint' => $checklistPoint->toArray(),
        ];
    }

    public function deleteChecklistPointAction(): array
    {
        $this->company()->checklistPoint()->delete(
            (int) $this->getApiParam('checklist_point_id'),
            $this->company->getId()
        );

        return [
            'is_deleted' => true,
        ];
    }

    public function deleteChecklistAction(): array
    {
        $this->company()->checklist()->delete(
            (int) $this->getApiParam('checklist_id'),
            $this->company->getId(),
        );

        return [
            'is_deleted' => true,
        ];
    }

    /**
     * Get common parameters for statistics methods
     */
    private function getStatisticsParams(bool $validateChecklistId = true): array
    {
        $companyId = $this->company->getId();
        $startDate = new Carbon($this->getApiParam('start_date'));
        $endDate = new Carbon($this->getApiParam('end_date'));
        $checklistId = $this->getApiParam('checklist_id') ? (int) $this->getApiParam('checklist_id') : null;

        if ($validateChecklistId && $checklistId !== null) {
            try {
                $this->company()->checklist()->getChecklist($checklistId, $companyId);
            } catch (NotFoundApiException $e) {
                throw new BadRequestApiException('Invalid checklist_id provided');
            }
        }

        return [
            'companyId' => $companyId,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'checklistId' => $checklistId,
        ];
    }

    private function getChecklistPointLastUpdate(int $companyId, int $checklistId)
    {
        return $this->company()->checklistPoint()->getChecklistPointLastUpdate(
            $companyId,
            $checklistId,
        );
    }

    private function validateEntityIdParam(string $paramName, string $errorMessage)
    {
        $value = $this->getApiParam($paramName);

        if (empty($value) || (is_numeric($value) && (int) $value <= 0)) {
            throw new BadRequestApiException($errorMessage);
        }

        return $value;
    }

    private function formatStatisticsResponse(array $statistics, ?Carbon $lastUpdate): array
    {
        return array_merge(
            $statistics,
            ['checkpoints_last_update' => $lastUpdate]
        );
    }

    private function sanitazeChecklistPoint()
    {

    }
}
